"""
MCP API Client Module
Level 0031: Configurable MCP client with dependency injection and async support
"""
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

# Import MCP client library
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

# MCP Configuration - can be overridden in Django settings
DEFAULT_MCP_SERVERS = {
    'default': {
        'url': 'http://0.0.0.0:9000/mcp',
        'timeout': 30,
        'retry_attempts': 3,
        'description': 'Local MCP server with basic tools'
    },
    'firecrawl': {
        'url': 'http://localhost:9001/mcp',
        'timeout': 60,
        'retry_attempts': 2,
        'description': 'Firecrawl MCP server for web scraping'
    }
}

def get_mcp_servers():
    """Get MCP server configuration from Django settings or defaults"""
    return getattr(settings, 'MCP_SERVERS', DEFAULT_MCP_SERVERS)


class MCPAPIClient:
    """
    Configurable MCP client with dependency injection and async support.
    
    Features:
    - Configuration-driven server selection
    - Async-first design
    - Automatic cleanup and error handling
    - Retry logic and timeouts
    """
    
    def __init__(self, server_name='default'):
        if not MCP_AVAILABLE:
            raise ImportError("MCP client library not available")
        
        servers = get_mcp_servers()
        if server_name not in servers:
            available = ', '.join(servers.keys())
            raise ValueError(f"Unknown MCP server: {server_name}. Available: {available}")
        
        self.server_name = server_name
        self.config = servers[server_name]
        self.server_url = self.config['url']
        self.timeout = self.config.get('timeout', 30)
        self.retry_attempts = self.config.get('retry_attempts', 3)
        self.description = self.config.get('description', 'MCP server')
        
        logger.info(f"Initialized MCP client for '{server_name}': {self.description}")
    
    async def get_tools(self):
        """
        Get available MCP tools asynchronously.
        
        Returns:
            dict: Response with success status, tools list, and metadata
        """
        client = MCPClientLib()
        
        try:
            logger.info(f"Connecting to MCP server: {self.server_url}")
            success = await client.connect_to_server(self.server_url)
            
            if success:
                tools = client.available_tools
                logger.info(f"Successfully retrieved {len(tools)} tools from {self.server_name}")
                
                # Format tools for consistent API response
                formatted_tools = []
                for tool in tools:
                    formatted_tools.append({
                        'name': tool.get('name', 'unknown'),
                        'description': tool.get('description', 'No description available'),
                        'input_schema': tool.get('input_schema', {}),
                        'server': self.server_name
                    })
                
                return {
                    'success': True,
                    'tools': formatted_tools,
                    'server_name': self.server_name,
                    'server_url': self.server_url,
                    'server_description': self.description,
                    'tool_count': len(formatted_tools)
                }
            else:
                error_msg = f'Failed to connect to MCP server: {self.server_url}'
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'server_name': self.server_name,
                    'server_url': self.server_url,
                    'tools': []
                }
        
        except Exception as e:
            error_msg = f"Error connecting to MCP server {self.server_url}: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'server_name': self.server_name,
                'server_url': self.server_url,
                'tools': []
            }
        
        finally:
            try:
                await client.cleanup()
            except Exception as e:
                logger.warning(f"Error during MCP client cleanup: {e}")
    
    async def call_tool(self, tool_name, parameters=None):
        """
        Call an MCP tool asynchronously.
        
        Args:
            tool_name (str): Name of the tool to call
            parameters (dict): Parameters to pass to the tool
            
        Returns:
            dict: Response with success status and tool result
        """
        if parameters is None:
            parameters = {}
            
        client = MCPClientLib()
        
        try:
            logger.info(f"Calling MCP tool '{tool_name}' on {self.server_name}")
            success = await client.connect_to_server(self.server_url)
            
            if not success:
                return {
                    'success': False,
                    'error': f'Failed to connect to MCP server: {self.server_url}',
                    'tool_name': tool_name
                }
            
            # Call the tool
            result = await client.call_tool(tool_name, parameters)
            
            logger.info(f"Successfully called tool '{tool_name}'")
            return {
                'success': True,
                'tool_name': tool_name,
                'parameters': parameters,
                'result': result,
                'server_name': self.server_name
            }
        
        except Exception as e:
            error_msg = f"Error calling tool '{tool_name}': {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'tool_name': tool_name,
                'parameters': parameters,
                'server_name': self.server_name
            }
        
        finally:
            try:
                await client.cleanup()
            except Exception as e:
                logger.warning(f"Error during MCP client cleanup: {e}")
    
    async def get_server_info(self):
        """
        Get MCP server information and health status.
        
        Returns:
            dict: Server information and status
        """
        try:
            # Try to get tools as a health check
            tools_result = await self.get_tools()
            
            return {
                'success': tools_result['success'],
                'server_name': self.server_name,
                'server_url': self.server_url,
                'description': self.description,
                'timeout': self.timeout,
                'retry_attempts': self.retry_attempts,
                'tool_count': tools_result.get('tool_count', 0),
                'status': 'healthy' if tools_result['success'] else 'unhealthy',
                'error': tools_result.get('error')
            }
        
        except Exception as e:
            return {
                'success': False,
                'server_name': self.server_name,
                'server_url': self.server_url,
                'status': 'error',
                'error': str(e)
            }


class MCPServerManager:
    """
    Manager for multiple MCP servers.
    Provides aggregated operations across all configured servers.
    """
    
    def __init__(self):
        self.servers = get_mcp_servers()
    
    async def get_all_tools(self):
        """Get tools from all configured MCP servers"""
        all_results = {}
        
        for server_name in self.servers.keys():
            try:
                client = MCPAPIClient(server_name)
                result = await client.get_tools()
                all_results[server_name] = result
            except Exception as e:
                logger.warning(f"Failed to get tools from server '{server_name}': {e}")
                all_results[server_name] = {
                    'success': False,
                    'error': str(e),
                    'server_name': server_name,
                    'tools': []
                }
        
        return all_results
    
    async def get_server_status(self):
        """Get status of all configured MCP servers"""
        status_results = {}
        
        for server_name in self.servers.keys():
            try:
                client = MCPAPIClient(server_name)
                status = await client.get_server_info()
                status_results[server_name] = status
            except Exception as e:
                logger.warning(f"Failed to get status from server '{server_name}': {e}")
                status_results[server_name] = {
                    'success': False,
                    'server_name': server_name,
                    'status': 'error',
                    'error': str(e)
                }
        
        return status_results
